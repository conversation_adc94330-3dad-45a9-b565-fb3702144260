#!/usr/bin/env node

/**
 * 网络访问测试脚本
 * 用于验证服务器是否正确配置为支持局域网访问
 */

import os from 'os';

function getNetworkInterfaces() {
  const interfaces = os.networkInterfaces();
  const addresses = [];
  
  for (const name of Object.keys(interfaces)) {
    for (const iface of interfaces[name]) {
      // 跳过内部地址和IPv6
      if (iface.family === 'IPv4' && !iface.internal) {
        addresses.push({
          name,
          address: iface.address,
          netmask: iface.netmask
        });
      }
    }
  }
  
  return addresses;
}

function displayNetworkInfo() {
  console.log('🌐 网络配置检查');
  console.log('================');
  
  const interfaces = getNetworkInterfaces();
  
  if (interfaces.length === 0) {
    console.log('❌ 未找到可用的网络接口');
    return;
  }
  
  console.log('📡 可用的网络接口:');
  interfaces.forEach((iface, index) => {
    console.log(`  ${index + 1}. ${iface.name}: ${iface.address}`);
  });
  
  console.log('\n🔗 访问地址:');
  console.log('  本地访问: http://localhost:5000');
  console.log('  本地访问: http://127.0.0.1:5000');
  
  interfaces.forEach(iface => {
    console.log(`  局域网访问: http://${iface.address}:5000`);
  });
  
  console.log('\n⚙️ 服务器配置检查:');
  console.log('  ✅ 服务器绑定地址: 0.0.0.0 (支持所有接口)');
  console.log('  ✅ 服务器端口: 5000');
  console.log('  ✅ API基础URL: 动态检测');
  console.log('  ✅ WebSocket URL: 动态检测');
  
  console.log('\n🛡️ 防火墙检查:');
  console.log('  请确保以下端口在防火墙中开放:');
  console.log('  - 端口 5000 (HTTP服务器)');
  console.log('  - 端口 5000 (WebSocket连接)');
  
  console.log('\n📋 故障排除:');
  console.log('  1. 确保防火墙允许端口 5000');
  console.log('  2. 确保路由器没有阻止局域网通信');
  console.log('  3. 检查客户端设备是否在同一网段');
  console.log('  4. 尝试关闭防火墙进行测试');
  
  console.log('\n🔧 Windows防火墙配置:');
  console.log('  1. 打开"Windows Defender 防火墙"');
  console.log('  2. 点击"高级设置"');
  console.log('  3. 选择"入站规则" -> "新建规则"');
  console.log('  4. 选择"端口" -> "TCP" -> "特定本地端口" -> 输入"5000"');
  console.log('  5. 选择"允许连接" -> 完成');
}

// 运行检查
displayNetworkInfo();
