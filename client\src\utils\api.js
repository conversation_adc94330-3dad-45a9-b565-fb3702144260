export function getApiBaseUrl() {
  if (typeof window !== 'undefined') {
    const { protocol, hostname, port } = window.location;

    // 在开发环境中 (包括localhost、127.0.0.1和局域网IP)
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.startsWith('192.168.') || hostname.startsWith('10.') || hostname.startsWith('172.')) {
      return `${protocol}//${hostname}:5000`;
    }

    // 在生产环境中，使用相对路径
    return '';
  }
  return '';
}

export function getWebSocketUrl() {
  if (typeof window !== 'undefined') {
    const { protocol, hostname, port } = window.location;
    const wsProtocol = protocol === 'https:' ? 'wss' : 'ws';

    // 在开发环境中 (包括localhost、127.0.0.1和局域网IP)
    if (hostname === 'localhost' || hostname === '127.0.0.1' || hostname.startsWith('192.168.') || hostname.startsWith('10.') || hostname.startsWith('172.')) {
      return `${wsProtocol}://${hostname}:5000/ws`;
    }

    // 在生产环境中
    return `${wsProtocol}://${hostname}${port ? ':' + port : ''}/ws`;
  }
  return '';
}