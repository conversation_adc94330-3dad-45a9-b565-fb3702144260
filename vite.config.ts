import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import { fileURLToPath, URL } from "node:url";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
//获取当前目录路径
//_dirname用于绝对路径
const __dirname = path.dirname(fileURLToPath(import.meta.url));

export default defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...(process.env.NODE_ENV !== "production" &&
    process.env.REPL_ID !== undefined
      ? [
          await import("@replit/vite-plugin-cartographer").then((m) =>
            m.cartographer(),
          ),
        ]
      : []),
  ],
  //路径别名：简化导入配置：@/components/Button → client/src/components/Button
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "client", "src"),
      "@shared": path.resolve(__dirname, "shared"),
      "@assets": path.resolve(__dirname, "attached_assets"),
    },
  },
  //项目根目录
  root: path.resolve(__dirname, "client"),
  //开发服务器配置
  server: {
    host: "0.0.0.0", // 允许局域网访问
    port: 5173, // Vite默认端口
  },
  //打包项目专用
  build: {
    //指定打包后的文件存放位置
    outDir: path.resolve(__dirname, "dist/public"),
    //每次构建前清空打包目录
    emptyOutDir: true,
  },
});
