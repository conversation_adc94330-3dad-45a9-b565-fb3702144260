import express from 'express';
import { createServer } from 'http';

const app = express();
const port = 5000;

// 基本中间件
app.use(express.json());

// 测试路由
app.get('/', (req, res) => {
  res.json({
    message: '🎉 服务器运行正常！',
    timestamp: new Date().toISOString(),
    host: req.get('host'),
    ip: req.ip,
    userAgent: req.get('user-agent')
  });
});

app.get('/api/test', (req, res) => {
  res.json({
    success: true,
    message: 'API 测试成功',
    data: {
      localAccess: 'http://localhost:5000',
      networkAccess: 'http://************:5000',
      timestamp: new Date().toISOString()
    }
  });
});

// 创建HTTP服务器
const server = createServer(app);

// 启动服务器
server.listen({
  port,
  host: "0.0.0.0", // 支持局域网访问
}, () => {
  console.log('🚀 测试服务器启动成功！');
  console.log(`📍 本地访问: http://localhost:${port}`);
  console.log(`🌐 局域网访问: http://************:${port}`);
  console.log('');
  console.log('🔗 测试链接:');
  console.log(`  - http://localhost:${port}`);
  console.log(`  - http://************:${port}`);
  console.log(`  - http://localhost:${port}/api/test`);
  console.log(`  - http://************:${port}/api/test`);
  console.log('');
  console.log('✅ 服务器配置正确，支持局域网访问！');
});

// 错误处理
server.on('error', (error) => {
  if (error.code === 'EADDRINUSE') {
    console.error(`❌ 端口 ${port} 已被占用`);
    console.log('请先停止其他使用该端口的进程，或使用不同的端口');
  } else {
    console.error('❌ 服务器启动失败:', error);
  }
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});
