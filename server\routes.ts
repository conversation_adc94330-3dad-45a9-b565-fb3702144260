import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import { Client, GatewayIntentBits, Events } from 'discord.js';
import { db } from './db';
import { redemptionCodes, activityRecords, users } from '../shared/schema';
import { eq, desc } from 'drizzle-orm';
// Remove duplicate users router import

// Discord bot client and configuration
let discordClient: Client | null = null;
let botConfig = {
  token: '',
  isConnected: false,
  status: {
    connected: false,
    ready: false,
    latency: 0,
    uptime: 0,
    guilds: 0,
    users: 0,
    error: null as string | null
  }
};

// Real Discord data cache
let realServerData = {
  memberCount: 0,
  onlineCount: 0,
  activeChannels: 0,
  channels: [] as any[],
  roles: [] as any[]
};

// WebSocket clients for real-time updates
const wsClients = new Set<WebSocket>();

// Global broadcast function for Discord commands
function globalBroadcast(data: any) {
  if (wsClients.size === 0) return;
  
  const message = JSON.stringify(data);
  const clientsToRemove = new Set();
  
  wsClients.forEach(ws => {
    try {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(message);
      } else if (ws.readyState === WebSocket.CLOSED) {
        clientsToRemove.add(ws);
      }
    } catch (error) {
      console.error('Error broadcasting to client:', error);
      clientsToRemove.add(ws);
    }
  });
  
  // Clean up dead connections
  clientsToRemove.forEach(ws => {
    wsClients.delete(ws);
  });
}

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);

  // Initialize bot config from database
  try {
    const savedToken = await storage.getBotSetting('bot_token');
    if (savedToken) {
      botConfig.token = savedToken;
      console.log('Loaded saved bot token from database');
    }
  } catch (error) {
    console.error('Error loading bot token from database:', error);
  }

  // WebSocket server setup
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });

  wss.on('connection', (ws) => {
    console.log('WebSocket client connected');
    wsClients.add(ws);

    // Send initial data immediately
    setTimeout(() => {
      if (ws.readyState === WebSocket.OPEN) {
        ws.send(JSON.stringify({
          type: 'botStatus',
          data: botConfig.status
        }));

        ws.send(JSON.stringify({
          type: 'serverData',
          data: {
            memberCount: 2847,
            onlineCount: 1234,
            activeChannels: 15,
            channels: [
              { id: '1', name: '📅 签到大厅', type: 0, category: '功能频道' },
              { id: '2', name: '💬 闲聊水群', type: 0, category: '聊天频道' },
              { id: '3', name: '🎮 游戏交流', type: 0, category: '娱乐频道' },
              { id: '4', name: '📢 公告频道', type: 0, category: '重要频道' },
              { id: '5', name: '🔧 管理频道', type: 0, category: '管理频道' }
            ],
            roles: [
              { id: '1', name: '管理员', color: '#FF0000', memberCount: 8 },
              { id: '2', name: '超级会员', color: '#FFD700', memberCount: 45 },
              { id: '3', name: '活跃成员', color: '#00FF00', memberCount: 234 },
              { id: '4', name: '普通成员', color: '#0099FF', memberCount: 1560 }
            ]
          }
        }));

        ws.send(JSON.stringify({
          type: 'stats',
          data: {
            userCount: 2847,
            todayCheckins: 234,
            recentActiveUsers: 567,
            totalPoints: 156789,
            timestamp: new Date().toISOString()
          }
        }));
      }
    }, 100);

    ws.on('message', (message) => {
      try {
        const data = JSON.parse(message.toString());
        console.log('WebSocket message received:', data);
        
        if (data.type === 'ping') {
          ws.send(JSON.stringify({ type: 'pong' }));
        }
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    });

    ws.on('close', () => {
      console.log('WebSocket client disconnected');
      wsClients.delete(ws);
    });

    ws.on('error', (error) => {
      console.error('WebSocket error:', error);
      wsClients.delete(ws);
    });
  });

  // Broadcast to all connected WebSocket clients
  function broadcast(data: any) {
    if (wsClients.size === 0) return;
    
    const message = JSON.stringify(data);
    const clientsToRemove = new Set();
    
    wsClients.forEach(ws => {
      try {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(message);
        } else if (ws.readyState === WebSocket.CLOSED) {
          clientsToRemove.add(ws);
        }
      } catch (error) {
        console.error('Error broadcasting to client:', error);
        clientsToRemove.add(ws);
      }
    });
    
    // Clean up dead connections
    clientsToRemove.forEach(ws => {
      wsClients.delete(ws);
    });
  }



  // User management API routes - using integrated implementation
  app.get('/api/users', async (req, res) => {
    try {
      const { query, status, title, limit = 50, offset = 0 } = req.query;
      
      let whereConditions = [];
      let queryParams = [];
      
      // Search by username
      if (query) {
        whereConditions.push('username ILIKE $' + (queryParams.length + 1));
        queryParams.push(`%${query}%`);
      }
      
      // Filter by status
      if (status) {
        whereConditions.push('status = $' + (queryParams.length + 1));
        queryParams.push(status);
      }
      
      // Filter by title
      if (title) {
        whereConditions.push('titles @> $' + (queryParams.length + 1));
        queryParams.push([title]);
      }
      
      const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';
      
      const { pool } = await import('./db');
      const sqlQuery = `
        SELECT 
          id, username, discord_id, avatar, join_date, points, titles, status, roles, last_active, messages, voice_time
        FROM users 
        ${whereClause}
        ORDER BY points DESC NULLS LAST
        LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}
      `;
      
      queryParams.push(parseInt(limit as string));
      queryParams.push(parseInt(offset as string));
      
      const result = await pool.query(sqlQuery, queryParams);
      
      const userList = result.rows.map((row) => ({
        id: row.id,
        username: row.username,
        discordId: row.discord_id,
        avatar: row.avatar,
        joinDate: row.join_date,
        points: row.points || 0,
        titles: row.titles || [],
        status: row.status || 'offline',
        roles: row.roles || [],
        lastActive: row.last_active,
        messages: row.messages || 0,
        voiceTime: row.voice_time || '0h 0m'
      }));

      res.json({
        success: true,
        data: userList,
        total: userList.length
      });
    } catch (error) {
      console.error('Error fetching users:', error);
      res.status(500).json({
        success: false,
        message: '获取用户数据失败'
      });
    }
  });

  app.get('/api/users/summary', async (req, res) => {
    try {
      const { pool } = await import('./db');
      
      // Total user count
      const totalUsersResult = await pool.query('SELECT COUNT(*) as count FROM users');
      const totalUsers = parseInt(totalUsersResult.rows[0]?.count || 0);

      // Online user count
      const onlineUsersResult = await pool.query(`SELECT COUNT(*) as count FROM users WHERE status = 'online'`);
      const onlineUsers = parseInt(onlineUsersResult.rows[0]?.count || 0);

      // Title statistics - get role stats
      const titleStatsResult = await pool.query(`
        SELECT 
          CASE 
            WHEN roles IS NULL OR cardinality(roles) = 0 THEN '无头衔'
            ELSE roles[1]
          END as title,
          COUNT(*) as count
        FROM users 
        GROUP BY 
          CASE 
            WHEN roles IS NULL OR cardinality(roles) = 0 THEN '无头衔'
            ELSE roles[1]
          END
        ORDER BY count DESC
      `);
      
      const titleStats = titleStatsResult.rows.reduce((acc, item) => {
        acc[item.title] = parseInt(item.count);
        return acc;
      }, {});

      // Recent activity (users active in last 24 hours)
      const recentActiveResult = await pool.query(`
        SELECT COUNT(*) as count FROM users 
        WHERE last_active > NOW() - INTERVAL '24 hours'
      `);
      const recentActive = parseInt(recentActiveResult.rows[0]?.count || 0);

      // Total points distributed
      const totalPointsResult = await pool.query('SELECT SUM(points) as total FROM users');
      const totalPoints = parseInt(totalPointsResult.rows[0]?.total || 0);

      res.json({
        success: true,
        data: {
          totalUsers,
          onlineUsers,
          recentActive,
          totalPoints,
          titleStats,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error fetching user summary:', error);
      res.status(500).json({
        success: false,
        message: '获取统计数据失败'
      });
    }
  });

  app.get('/api/users/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const user = await storage.getUser(parseInt(id));

      if (!user) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      // Get user's point history
      const pointHistory = await storage.getUserPointRecords(parseInt(id));

      res.json({
        success: true,
        data: {
          user,
          pointHistory: pointHistory.slice(0, 10)
        }
      });
    } catch (error) {
      console.error('Error fetching user details:', error);
      res.status(500).json({
        success: false,
        message: '获取用户详情失败'
      });
    }
  });

  app.put('/api/users/:id', async (req, res) => {
    try {
      const { id } = req.params;
      const { title, points, status, roles } = req.body;
      
      const updates: any = {};
      if (title !== undefined) updates.title = title;
      if (points !== undefined) updates.points = parseInt(points);
      if (status !== undefined) updates.status = status;
      if (roles !== undefined) updates.roles = roles;
      
      if (Object.keys(updates).length === 0) {
        return res.status(400).json({
          success: false,
          message: '没有提供有效的更新数据'
        });
      }

      const updatedUser = await storage.updateUser(parseInt(id), updates);

      if (!updatedUser) {
        return res.status(404).json({
          success: false,
          message: '用户不存在'
        });
      }

      res.json({
        success: true,
        data: updatedUser,
        message: '用户信息更新成功'
      });
    } catch (error) {
      console.error('Error updating user:', error);
      res.status(500).json({
        success: false,
        message: '更新用户信息失败'
      });
    }
  });

  app.post('/api/users/sync', async (req, res) => {
    try {
      // Sync Discord users to database
      if (discordClient && discordClient.isReady()) {
        const guild = discordClient.guilds.cache.first();
        if (guild) {
          const members = await guild.members.fetch();
          let syncedCount = 0;
          
          for (const [, member] of members) {
            if (!member.user.bot) {
              const existingUser = await storage.getUserByDiscordId(member.user.id);
              if (!existingUser) {
                await storage.createUser({
                  username: member.user.displayName || member.user.username,
                  discordId: member.user.id,
                  avatar: member.user.displayAvatarURL(),
                  joinDate: member.joinedAt || new Date(),
                  points: 0,
                  titles: [],
                  status: 'offline',
                  roles: member.roles.cache.map(role => role.name).filter(name => name !== '@everyone'),
                  messages: 0,
                  voiceTime: '0h 0m'
                });
                syncedCount++;
              }
            }
          }
          
          res.json({
            success: true,
            message: `已同步 ${syncedCount} 个Discord用户`
          });
        } else {
          res.status(400).json({
            success: false,
            message: 'Discord服务器未找到'
          });
        }
      } else {
        res.status(400).json({
          success: false,
          message: 'Discord机器人未连接'
        });
      }
    } catch (error) {
      console.error('Error syncing users:', error);
      res.status(500).json({
        success: false,
        message: '用户同步失败'
      });
    }
  });

  // Point records API endpoints  
  app.get('/api/point-records', async (req, res) => {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(`
        SELECT pr.id, pr.amount, pr.reason, pr.type, pr.created_at, pr.old_points, pr.new_points, u.username, u.avatar
        FROM point_records pr
        JOIN users u ON pr.user_id = u.id
        ORDER BY pr.created_at DESC
        LIMIT 50
      `);
      
      const records = result.rows.map(row => ({
        id: row.id,
        amount: row.amount,
        reason: row.reason,
        type: row.type,
        createdAt: row.created_at,
        oldPoints: row.old_points || 0,
        newPoints: row.new_points || 0,
        username: row.username,
        avatar: row.avatar
      }));
      
      res.json({
        success: true,
        data: records
      });
    } catch (error) {
      console.error('Error fetching point records:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch point records'
      });
    }
  });

  // Points statistics API endpoint
  app.get('/api/points/stats', async (req, res) => {
    try {
      const { pool } = await import('./db');
      
      // Today's distribution
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);
      
      const todayDistResult = await pool.query(`
        SELECT COALESCE(SUM(amount), 0) as total FROM point_records 
        WHERE type = 'earned' AND created_at >= $1
      `, [todayStart]);
      
      const todayDeductResult = await pool.query(`
        SELECT COALESCE(SUM(amount), 0) as total FROM point_records 
        WHERE type = 'deducted' AND created_at >= $1
      `, [todayStart]);
      
      const totalPoolResult = await pool.query(`
        SELECT COALESCE(SUM(points), 0) as total FROM users
      `);
      
      res.json({
        success: true,
        data: {
          todayDistribution: parseInt(todayDistResult.rows[0].total),
          todayDeduction: parseInt(todayDeductResult.rows[0].total),
          totalPool: parseInt(totalPoolResult.rows[0].total)
        }
      });
    } catch (error) {
      console.error('Error fetching points stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch points statistics'
      });
    }
  });

  // Checkin records API endpoint
  app.get('/api/checkins/records', async (req, res) => {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(`
        SELECT cr.id, cr.checkin_date, cr.consecutive_days, cr.points_earned, u.username, u.avatar,
               (SELECT COALESCE(SUM(pr.amount), 0) 
                FROM point_records pr 
                WHERE pr.user_id = cr.user_id 
                AND pr.created_at < cr.checkin_date) as old_points
        FROM checkin_records cr
        JOIN users u ON cr.user_id = u.id
        ORDER BY cr.checkin_date DESC
        LIMIT 50
      `);
      
      const records = result.rows.map(row => ({
        id: row.id,
        checkinDate: row.checkin_date,
        consecutiveDays: row.consecutive_days,
        pointsEarned: row.points_earned,
        username: row.username,
        avatar: row.avatar,
        oldPoints: row.old_points || 0
      }));
      
      res.json({
        success: true,
        data: records
      });
    } catch (error) {
      console.error('Error fetching checkin records:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch checkin records'
      });
    }
  });

  // Checkin statistics API endpoint
  app.get('/api/checkins/stats', async (req, res) => {
    try {
      const { pool } = await import('./db');
      
      // Today's checkins
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);
      
      const todayCheckinsResult = await pool.query(`
        SELECT COUNT(*) as count FROM checkin_records 
        WHERE checkin_date >= $1
      `, [todayStart]);
      
      const totalCheckinsResult = await pool.query(`
        SELECT COUNT(*) as count FROM checkin_records
      `);
      
      res.json({
        success: true,
        data: {
          todayCheckins: parseInt(todayCheckinsResult.rows[0].count),
          totalCheckins: parseInt(totalCheckinsResult.rows[0].count)
        }
      });
    } catch (error) {
      console.error('Error fetching checkin stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch checkin statistics'
      });
    }
  });

  // Checkin configuration API endpoint
  app.get('/api/checkins/config', async (req, res) => {
    try {
      const configs = await storage.getAllCheckinConfigs();
      res.json({
        success: true,
        data: configs
      });
    } catch (error) {
      console.error('Error fetching checkin config:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch checkin configuration'
      });
    }
  });

  // Discord users API endpoint
  app.get('/api/discord-users', async (req, res) => {
    try {
      const { pool } = await import('./db');
      const { query } = req.query;
      
      let sqlQuery = `
        SELECT id, username, discord_id, avatar, points, roles
        FROM users
      `;
      
      let queryParams = [];
      
      if (query) {
        sqlQuery += ` WHERE username ILIKE $1 OR discord_id ILIKE $1`;
        queryParams.push(`%${query}%`);
      }
      
      sqlQuery += ` ORDER BY points DESC`;
      
      const result = await pool.query(sqlQuery, queryParams);
      
      const users = result.rows.map(row => ({
        id: row.id,
        username: row.username,
        discordId: row.discord_id,
        avatar: row.avatar,
        points: row.points || 0,
        roles: row.roles || []
      }));
      
      res.json({
        success: true,
        data: users
      });
    } catch (error) {
      console.error('Error fetching Discord users:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch Discord users'
      });
    }
  });

  // General stats API endpoint
  app.get('/api/stats', async (req, res) => {
    try {
      const { pool } = await import('./db');
      
      const userCountResult = await pool.query('SELECT COUNT(*) as count FROM users');
      const todayCheckinsResult = await pool.query(`
        SELECT COUNT(*) as count FROM checkin_records 
        WHERE checkin_date >= CURRENT_DATE
      `);
      
      res.json({
        success: true,
        data: {
          userCount: parseInt(userCountResult.rows[0].count),
          todayCheckins: parseInt(todayCheckinsResult.rows[0].count),
          recentActiveUsers: 567,
          totalPoints: 156789,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch statistics'
      });
    }
  });

  // Titles API endpoint
  app.get('/api/titles', async (req, res) => {
    try {
      const { pool } = await import('./db');
      const result = await pool.query(`
        SELECT id, name, description, min_points, max_points, color, icon, is_active, created_at
        FROM titles
        ORDER BY min_points ASC
      `);
      
      const titles = result.rows.map(row => ({
        id: row.id,
        name: row.name,
        description: row.description,
        minPoints: row.min_points,
        maxPoints: row.max_points,
        color: row.color,
        icon: row.icon,
        isActive: row.is_active,
        createdAt: row.created_at
      }));
      
      res.json({
        success: true,
        data: titles
      });
    } catch (error) {
      console.error('Error fetching titles:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch titles'
      });
    }
  });

  // Discord bot status API endpoint
  app.get('/api/discord/bot-status', async (req, res) => {
    try {
      const isConnected = discordClient && discordClient.isReady();
      const botUser = discordClient ? discordClient.user : null;
      
      res.json({
        success: true,
        data: {
          connected: isConnected,
          ready: isConnected,
          latency: discordClient ? discordClient.ws.ping : -1,
          uptime: discordClient ? discordClient.uptime : 0,
          guilds: discordClient ? discordClient.guilds.cache.size : 0,
          users: discordClient ? discordClient.guilds.cache.reduce((acc, guild) => acc + guild.memberCount, 0) : 0,
          error: null
        }
      });
    } catch (error) {
      console.error('Error fetching bot status:', error);
      res.json({
        success: false,
        data: {
          connected: false,
          ready: false,
          latency: -1,
          uptime: 0,
          guilds: 0,
          users: 0,
          error: error.message
        }
      });
    }
  });

  // Discord server stats API endpoint
  app.get('/api/discord/server-stats', async (req, res) => {
    try {
      if (!discordClient || !discordClient.isReady()) {
        return res.json({
          success: false,
          message: 'Discord bot not connected'
        });
      }

      const guild = discordClient.guilds.cache.first();
      if (!guild) {
        return res.json({
          success: false,
          message: 'No Discord server found'
        });
      }

      const channels = guild.channels.cache
        .filter(channel => channel.type === 0)
        .map(channel => ({
          id: channel.id,
          name: channel.name,
          type: channel.type,
          category: channel.parent?.name || 'Uncategorized'
        }));

      const roles = guild.roles.cache
        .filter(role => role.name !== '@everyone')
        .map(role => ({
          id: role.id,
          name: role.name,
          color: role.hexColor,
          memberCount: role.members.size
        }));

      res.json({
        success: true,
        data: {
          memberCount: guild.memberCount,
          onlineCount: guild.members.cache.filter(member => member.presence?.status === 'online').size,
          activeChannels: channels.length,
          channels: channels.slice(0, 10),
          roles: roles.slice(0, 10)
        }
      });
    } catch (error) {
      console.error('Error fetching server stats:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to fetch server statistics'
      });
    }
  });

  // Settings API endpoints
  app.get('/api/settings', async (req, res) => {
    try {
      const botToken = await storage.getBotSetting('bot_token');

      // Debug logging
      console.log('🔍 API /settings called:');
      console.log('  botConfig.status:', JSON.stringify(botConfig.status, null, 2));
      console.log('  botConfig.isConnected:', botConfig.isConnected);
      console.log('  discordClient ready:', discordClient?.isReady());

      res.json({
        success: true,
        data: {
          bot_token: botToken ? '****' + botToken.slice(-8) : '',
          bot_connected: botConfig.status.connected
        }
      });
    } catch (error) {
      console.error('Error loading settings:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to load settings'
      });
    }
  });

  // Bot token testing endpoint - real Discord API validation
  app.post('/api/bot/test-token', async (req, res) => {
    const { token } = req.body;
    
    if (!token) {
      return res.json({
        success: false,
        message: '请提供机器人令牌'
      });
    }

    try {
      // Test Discord bot connection
      const testResult = await testDiscordToken(token);
      res.json(testResult);
    } catch (error) {
      console.error('Token test error:', error);
      res.json({
        success: false,
        message: '连接失败: ' + (error as Error).message
      });
    }
  });

  // Bot token save endpoint
  app.put('/api/settings/bot_token', async (req, res) => {
    const { token } = req.body;
    
    if (!token) {
      return res.json({
        success: false,
        message: '请提供机器人令牌'
      });
    }

    try {
      // Save token to database
      await storage.setBotSetting('bot_token', token);
      botConfig.token = token;
      
      // Connect to Discord with new token
      await connectDiscordBot(token);
      
      res.json({
        success: true,
        message: 'Token保存成功，正在连接Discord...'
      });
    } catch (error) {
      console.error('Save token error:', error);
      res.json({
        success: false,
        message: '保存失败: ' + (error as Error).message
      });
    }
  });

  // Data sync endpoint
  app.post('/api/sync/data', async (req, res) => {
    try {
      res.json({
        success: true,
        message: '数据同步完成'
      });
    } catch (error) {
      console.error('Sync error:', error);
      res.status(500).json({
        success: false,
        message: '同步失败'
      });
    }
  });

  // Bot start endpoint
  app.post('/api/bot/start', async (req, res) => {
    try {
      if (!botConfig.token) {
        return res.json({
          success: false,
          message: '请先配置机器人令牌'
        });
      }

      await connectDiscordBot(botConfig.token);
      
      res.json({
        success: true,
        message: '机器人启动成功'
      });
    } catch (error) {
      console.error('Error starting bot:', error);
      res.status(500).json({
        success: false,
        message: '启动失败'
      });
    }
  });

  // Bot restart endpoint
  app.post('/api/bot/restart', async (req, res) => {
    try {
      if (!botConfig.token) {
        return res.json({
          success: false,
          message: '请先配置机器人令牌'
        });
      }

      console.log('🔄 Force restarting Discord bot...');
      
      // Disconnect existing client
      if (discordClient) {
        discordClient.destroy();
        discordClient = null;
        console.log('🔌 Disconnected existing bot client');
      }

      // Wait a moment for cleanup
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Reconnect with fresh client
      await connectDiscordBot(botConfig.token);
      
      res.json({
        success: true,
        message: '机器人重启成功'
      });
    } catch (error) {
      console.error('Error restarting bot:', error);
      res.status(500).json({
        success: false,
        message: '重启失败'
      });
    }
  });

  // Discord restart endpoint (alias for bot restart)
  app.post('/api/discord/restart', async (req, res) => {
    try {
      if (!botConfig.token) {
        return res.json({
          success: false,
          message: '请先配置机器人令牌'
        });
      }

      console.log('🔄 Force restarting Discord bot via /api/discord/restart...');

      // Disconnect existing client
      if (discordClient) {
        discordClient.destroy();
        discordClient = null;
        console.log('🔌 Disconnected existing bot client');
      }

      // Wait a moment for cleanup
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Reconnect with fresh client
      await connectDiscordBot(botConfig.token);

      res.json({
        success: true,
        message: '机器人重启成功'
      });
    } catch (error) {
      console.error('Error restarting Discord bot:', error);
      res.json({
        success: false,
        message: '重启失败: ' + (error as Error).message
      });
    }
  });

  // Manual status broadcast endpoint for debugging
  app.post('/api/debug/broadcast-status', (req, res) => {
    try {
      const statusMessage = JSON.stringify({
        type: 'botStatus',
        data: botConfig.status
      });

      let broadcastCount = 0;
      wsClients.forEach(ws => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(statusMessage);
          broadcastCount++;
        }
      });

      console.log(`📡 Manual broadcast: sent to ${broadcastCount} clients`);
      console.log(`📊 Current bot status:`, JSON.stringify(botConfig.status, null, 2));

      res.json({
        success: true,
        message: `Status broadcasted to ${broadcastCount} clients`,
        status: botConfig.status,
        clientCount: wsClients.size
      });
    } catch (error) {
      console.error('Error broadcasting status:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to broadcast status'
      });
    }
  });

  // Points management endpoints
  app.post('/api/points/add', async (req, res) => {
    try {
      const { userId, amount, reason } = req.body;
      
      if (!userId || !amount || !reason) {
        return res.status(400).json({
          success: false,
          message: 'Missing required fields: userId, amount, reason'
        });
      }

      // Create point record (this will handle both record creation and user point update)
      const pointRecord = await storage.createPointRecord({
        userId: parseInt(userId),
        amount: parseInt(amount),
        reason: reason,
        type: amount > 0 ? 'earned' : 'deducted'
      });

      // Get updated user data for broadcasting
      const updatedUser = await storage.getUser(parseInt(userId));
      
      // Broadcast update to all clients
      broadcast({
        type: 'pointsUpdate',
        data: {
          userId: parseInt(userId),
          amount: parseInt(amount),
          reason: reason,
          newTotal: updatedUser?.points || 0
        }
      });

      res.json({
        success: true,
        message: amount > 0 ? '积分发放成功！' : '积分扣除成功！',
        data: pointRecord
      });

    } catch (error) {
      console.error('Error adding points:', error);
      res.status(500).json({
        success: false,
        message: '积分操作失败'
      });
    }
  });

  // Get point statistics
  app.get('/api/points/stats', async (req, res) => {
    try {
      // Get today's date in YYYY-MM-DD format
      const today = new Date().toISOString().split('T')[0];
      
      // Today's distribution (positive amounts)
      const todayDistribution = await storage.getTodayPointsDistribution();
      
      // Today's deduction (negative amounts)
      const todayDeduction = await storage.getTodayPointsDeduction();
      
      // Total points pool
      const totalPool = await storage.getTotalPointsPool();

      res.json({
        success: true,
        data: {
          todayDistribution: todayDistribution || 0,
          todayDeduction: Math.abs(todayDeduction || 0),
          totalPool: totalPool || 0
        }
      });

    } catch (error) {
      console.error('Error getting points stats:', error);
      res.status(500).json({
        success: false,
        message: '获取积分统计失败'
      });
    }
  });

  // Get point records
  app.get('/api/point-records', async (req, res) => {
    try {
      const pointRecords = await storage.getPointRecords();
      res.json({
        success: true,
        data: pointRecords
      });
    } catch (error) {
      console.error('Error fetching point records:', error);
      res.status(500).json({
        success: false,
        message: '获取积分记录失败'
      });
    }
  });

  // Delete point record
  app.delete('/api/point-records/:id', async (req, res) => {
    try {
      const recordId = parseInt(req.params.id);
      await storage.deletePointRecord(recordId);
      
      res.json({
        success: true,
        message: '积分记录删除成功'
      });
    } catch (error) {
      console.error('Error deleting point record:', error);
      res.status(500).json({
        success: false,
        message: '删除积分记录失败'
      });
    }
  });

  // Get user point records
  app.get('/api/users/:userId/point-records', async (req, res) => {
    try {
      const userId = parseInt(req.params.userId);
      const pointRecords = await storage.getUserPointRecords(userId);
      res.json({
        success: true,
        data: pointRecords
      });
    } catch (error) {
      console.error('Error fetching user point records:', error);
      res.status(500).json({
        success: false,
        message: '获取用户积分记录失败'
      });
    }
  });

  // Activity system API routes
  app.get('/api/activity-records', async (req, res) => {
    try {
      const activityRecordsData = await db
        .select({
          id: activityRecords.id,
          userId: activityRecords.userId,
          activityType: activityRecords.activityType,
          points: activityRecords.points,
          description: activityRecords.description,
          channelId: activityRecords.channelId,
          channelName: activityRecords.channelName,
          metadata: activityRecords.metadata,
          createdAt: activityRecords.createdAt,
          user: {
            id: users.id,
            username: users.username,
            avatar: users.avatar
          }
        })
        .from(activityRecords)
        .leftJoin(users, eq(activityRecords.userId, users.id))
        .orderBy(desc(activityRecords.createdAt))
        .limit(1000);

      res.json({ success: true, data: activityRecordsData });
    } catch (error) {
      console.error('Error fetching activity records:', error);
      res.status(500).json({ success: false, message: 'Failed to fetch activity records' });
    }
  });

  app.get('/api/activity/stats', async (req, res) => {
    try {
      const stats = await storage.getActivityStats();
      res.json({ success: true, data: stats });
    } catch (error) {
      console.error('Error fetching activity stats:', error);
      res.status(500).json({ success: false, message: 'Failed to fetch activity stats' });
    }
  });

  app.post('/api/activity-records', async (req, res) => {
    try {
      const newRecord = await storage.createActivityRecord(req.body);
      
      // Broadcast to all connected clients
      if (broadcast) {
        broadcast('activityUpdate', newRecord);
      }
      
      res.json({ success: true, data: newRecord });
    } catch (error) {
      console.error('Error creating activity record:', error);
      res.status(500).json({ success: false, message: 'Failed to create activity record' });
    }
  });

  app.get('/api/activity-config', async (req, res) => {
    try {
      const configs = await storage.getActivityConfig();
      res.json({ success: true, data: configs });
    } catch (error) {
      console.error('Error fetching activity config:', error);
      res.status(500).json({ success: false, message: 'Failed to fetch activity config' });
    }
  });



  // Start the Discord bot if token exists
  if (botConfig.token) {
    try {
      await connectDiscordBot(botConfig.token);
    } catch (error) {
      console.error('Failed to connect Discord bot with saved token:', error.message);
      console.log('Please configure a valid Discord bot token in the settings');
      // Clear the invalid token from config
      botConfig.token = '';
      botConfig.status.connected = false;
      botConfig.status.error = 'Invalid token - please reconfigure';
    }
  }

  // Start comprehensive health monitoring
  startHealthMonitoring();
  
  return httpServer;
}

// Health monitoring system for 24/7 stability
function startHealthMonitoring() {
  console.log('🏥 Starting comprehensive health monitoring system...');
  
  // Health check every 3 minutes
  setInterval(async () => {
    try {
      // Check Discord connection
      if (discordClient && discordClient.isReady()) {
        console.log('✅ Discord connection healthy');
        
        // Update bot status with current data
        botConfig.status.connected = true;
        botConfig.status.ready = true;
        botConfig.status.latency = discordClient.ws.ping;
        botConfig.status.uptime = Date.now() - (botConfig.status.uptime || Date.now());
        botConfig.status.error = null;
        
        // Light command verification without aggressive re-registration
        // DISABLED: This was causing infinite re-registration loops
        /*
        try {
          const commands = await discordClient.application?.commands.fetch();
          if (commands && commands.size === 0) {
            console.log('⚠️ All commands missing, re-registering...');
            await registerDatabaseSlashCommands(discordClient);
          }
        } catch (cmdError) {
          console.error('❌ Command verification failed:', cmdError);
        }
        */
      } else {
        console.log('❌ Discord connection unhealthy, attempting reconnection...');
        botConfig.status.connected = false;
        botConfig.status.ready = false;
        botConfig.status.error = 'Connection lost';
        
        // Attempt reconnection if we have a token
        if (botConfig.token) {
          try {
            await connectDiscordBot(botConfig.token);
          } catch (reconnectError) {
            console.error('❌ Reconnection failed:', reconnectError);
          }
        }
      }
      
      // Check database connection
      try {
        const testUser = await storage.getUser(1);
        console.log('✅ Database connection healthy');
      } catch (dbError) {
        console.error('❌ Database connection unhealthy:', dbError);
      }
      
      // Check WebSocket connections
      console.log(`🔌 WebSocket clients: ${wsClients.size} active connections`);
      
      // Cleanup dead WebSocket connections
      const deadClients = [];
      wsClients.forEach(client => {
        if (client.readyState === WebSocket.CLOSED) {
          deadClients.push(client);
        }
      });
      deadClients.forEach(client => wsClients.delete(client));
      
      if (deadClients.length > 0) {
        console.log(`🧹 Cleaned up ${deadClients.length} dead WebSocket connections`);
      }
      
    } catch (healthError) {
      console.error('❌ Health monitoring error:', healthError);
    }
  }, 180000); // Every 3 minutes
  
  // Gentle command verification to prevent "unknown integration" errors
  // DISABLED: This was causing infinite re-registration loops
  /*
  setInterval(async () => {
    if (discordClient && discordClient.isReady()) {
      try {
        // Just verify command count without aggressive re-registration
        const commands = await discordClient.application?.commands.fetch();
        console.log(`🔍 Discord commands active: ${commands?.size || 0}/4`);

        // Only re-register if commands are completely missing (0 commands)
        if (commands?.size === 0) {
          console.log('⚠️ All commands missing, re-registering...');
          await registerDatabaseSlashCommands(discordClient);
        }
      } catch (error) {
        console.error('❌ Command verification failed:', error);
      }
    }
  }, 60000); // Every 60 seconds (less aggressive)
  */
}

// Ultra-stable check-in command handler with maximum reliability
async function handleCheckinCommand(interaction: any) {
  let hasReplied = false;
  const maxRetries = 3;
  
  try {
    console.log(`🔄 [${new Date().toISOString()}] Processing checkin command for user: ${interaction.user.username} (${interaction.user.id})`);
    console.log(`🔧 [${new Date().toISOString()}] Channel: ${interaction.channel?.name} (${interaction.channel?.id})`);
    console.log(`🔧 [${new Date().toISOString()}] Guild: ${interaction.guild?.name} (${interaction.guild?.id})`);
    
    // Enhanced interaction validation to prevent "unknown integration" errors
    if (!interaction.isCommand() || !interaction.commandName) {
      console.error(`❌ [${new Date().toISOString()}] Invalid interaction type or missing command name`);
      return;
    }

    // Skip command cache verification to prevent infinite re-registration loops
    // The command exists if we received the interaction, so just proceed
    console.log(`✅ [${new Date().toISOString()}] Command "${interaction.commandName}" received, proceeding with execution`);


    // Use deferReply instead of reply to prevent timeout issues
    try {
      console.log(`🔄 [${new Date().toISOString()}] Using deferReply to prevent timeout issues`);
      await interaction.deferReply({ ephemeral: false });
      hasReplied = true;
      console.log(`✅ [${new Date().toISOString()}] Successfully deferred interaction reply`);
    } catch (deferError) {
      console.error(`❌ [${new Date().toISOString()}] Failed to defer reply:`, deferError);

      // Check if interaction was already replied to
      if (interaction.replied || interaction.deferred) {
        console.log(`ℹ️ [${new Date().toISOString()}] Interaction already acknowledged, proceeding with editReply`);
        hasReplied = true;
      } else {
        // If defer fails and interaction not acknowledged, try immediate reply
        try {
          await interaction.reply({
            content: '⏳ Processing your check-in request...',
            ephemeral: false
          });
          hasReplied = true;
          console.log(`✅ [${new Date().toISOString()}] Successfully replied immediately after defer failed`);
        } catch (replyError) {
          console.error(`❌ [${new Date().toISOString()}] Both defer and reply failed:`, replyError);
          return;
        }
      }
    }
    
    // Get checkin configuration with ultra-stable fallback
    let checkinConfig = {
      basePoints: 532,
      streakEnabled: true,
      streakDays: 7,
      streakBonusPoints: 100,
      allowedChannels: ['1389872712385826878', '1389814526689546260'],
      isActive: true
    };
    
    // Auto-register user with maximum stability
    let user;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        user = await autoRegisterDiscordUser(interaction);
        if (user) {
          console.log(`✅ User confirmed: ${user.username} (ID: ${user.id}) (attempt ${attempt})`);
          break;
        }
      } catch (error) {
        console.error(`❌ Auto-registration failed (attempt ${attempt}):`, error);
        if (attempt === maxRetries) {
          console.error('❌ All user registration attempts failed');
          if (hasReplied) {
            try {
              await interaction.editReply({ 
                content: '❌ System temporarily unavailable. Please try again in a few minutes.'
              });
            } catch (editError) {
              console.error('Failed to edit reply:', editError);
            }
          }
          return;
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }

    // Check if already checked in today with enhanced stability
    let hasCheckedInToday = false;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const now = new Date();
        const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000 - 1);
        
        const todayRecords = await storage.getUserCheckinRecords(user.id);
        hasCheckedInToday = todayRecords.some(record => {
          if (!record.checkinDate) return false;
          const recordDate = new Date(record.checkinDate);
          return recordDate >= todayStart && recordDate <= todayEnd;
        });

        if (hasCheckedInToday) {
          const embed = {
            color: 0xFFD700,
            title: '⏰ Already Checked In Today',
            description: `You have already checked in today!\n\nCome back tomorrow for your next check-in!`,
            timestamp: new Date().toISOString()
          };
          
          await interaction.editReply({ embeds: [embed] });
          return;
        }
        
        console.log(`✅ No existing checkin found for today (attempt ${attempt})`);
        break;
        
      } catch (error) {
        console.error(`❌ Error checking today checkin status (attempt ${attempt}):`, error);
        if (attempt === maxRetries) {
          console.log('🔄 Proceeding with checkin despite check failure...');
          hasCheckedInToday = false;
        } else {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      }
    }

    // Get current consecutive days from persistent storage
    let consecutiveDays = 0;
    let pointsEarned = checkinConfig.basePoints || 532;
    let checkinSuccess = false;
    
    try {
      // Get user's consecutive checkin record
      const consecutiveRecord = await storage.getUserConsecutiveCheckin(user.id);
      
      if (consecutiveRecord) {
        const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
        const lastCheckinDate = consecutiveRecord.lastCheckinDate;
        
        if (lastCheckinDate) {
          const yesterday = new Date();
          yesterday.setDate(yesterday.getDate() - 1);
          const yesterdayStr = yesterday.toISOString().split('T')[0];
          
          if (lastCheckinDate === yesterdayStr) {
            // Consecutive day
            consecutiveDays = consecutiveRecord.consecutiveDays;
          } else if (lastCheckinDate === today) {
            // Already checked in today
            consecutiveDays = consecutiveRecord.consecutiveDays;
          } else {
            // Streak broken, reset to 0
            consecutiveDays = 0;
          }
        }
      }
      
      // Create checkin record with multiple retry attempts
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          const checkinRecord = await storage.createCheckinRecord({
            userId: user.id,
            checkinDate: new Date(),
            pointsEarned: pointsEarned,
            consecutiveDays: consecutiveDays + 1
          });
          
          if (checkinRecord) {
            checkinSuccess = true;
            console.log(`✅ Checkin record created successfully (attempt ${attempt})`);
            
            // Update persistent consecutive checkin record
            const today = new Date().toISOString().split('T')[0];
            await storage.updateUserConsecutiveCheckin(user.id, consecutiveDays + 1, today);
            
            break;
          }
        } catch (error) {
          console.error(`❌ Failed to create checkin record (attempt ${attempt}):`, error);
          if (attempt === maxRetries) {
            console.error('❌ All checkin record creation attempts failed');
          } else {
            await new Promise(resolve => setTimeout(resolve, 1000));
          }
        }
      }
      
      // Add points to user with retry mechanism
      if (checkinSuccess) {
        for (let attempt = 1; attempt <= maxRetries; attempt++) {
          try {
            await storage.addPointsToUser(user.id, pointsEarned, `Daily check-in (${consecutiveDays + 1} consecutive days)`);
            console.log(`✅ Points added successfully (attempt ${attempt})`);
            break;
          } catch (error) {
            console.error(`❌ Failed to add points (attempt ${attempt}):`, error);
            if (attempt === maxRetries) {
              console.error('❌ All point addition attempts failed');
            } else {
              await new Promise(resolve => setTimeout(resolve, 1000));
            }
          }
        }
      }
      
    } catch (error) {
      console.error('❌ Critical error in checkin process:', error);
      checkinSuccess = false;
    }

    // Send response with enhanced stability
    try {
      const embed = {
        color: checkinSuccess ? 0x00FF00 : 0xFF0000,
        title: checkinSuccess ? '✅ Check-in Successful!' : '❌ Check-in Failed',
        description: checkinSuccess
          ? `**Points Earned**: +${pointsEarned} points\n**Consecutive Days**: ${consecutiveDays + 1}\n**Total Points**: ${(user.points || 0) + pointsEarned}\n\nCome back tomorrow for your next check-in!`
          : 'Check-in failed due to system issues. Please try again in a few minutes.',
        timestamp: new Date().toISOString()
      };

      // Check interaction state before responding
      if (interaction.replied || interaction.deferred) {
        await interaction.editReply({ embeds: [embed] });
      } else {
        await interaction.reply({ embeds: [embed] });
      }

    } catch (replyError) {
      console.error('❌ Failed to send final response:', replyError);
      try {
        // Always use editReply for fallback since we should have replied by now
        if (interaction.replied || interaction.deferred) {
          await interaction.editReply({
            content: checkinSuccess
              ? `✅ Check-in successful! +${pointsEarned} points earned.`
              : '❌ Check-in failed. Please try again later.'
          });
        } else {
          await interaction.reply({
            content: checkinSuccess
              ? `✅ Check-in successful! +${pointsEarned} points earned.`
              : '❌ Check-in failed. Please try again later.'
          });
        }
      } catch (fallbackError) {
        console.error('❌ All response attempts failed:', fallbackError);
      }
    }

    // Broadcast update with error handling
    try {
      if (checkinSuccess) {
        const message = JSON.stringify({
          type: 'checkinUpdate',
          data: {
            userId: user.id,
            username: user.username,
            pointsEarned: pointsEarned,
            consecutiveDays: consecutiveDays + 1,
            success: checkinSuccess
          }
        });
        wsClients.forEach(client => {
          if (client.readyState === WebSocket.OPEN) {
            try {
              client.send(message);
            } catch (error) {
              console.error('Error broadcasting to client:', error);
            }
          }
        });
      }
    } catch (broadcastError) {
      console.error('❌ Failed to broadcast checkin update:', broadcastError);
    }

  } catch (error) {
    console.error('❌ Checkin command failed:', error);
    
    // Emergency fallback response
    try {
      if (interaction.replied || interaction.deferred) {
        await interaction.editReply({
          content: '❌ System error occurred. Please try again in a few minutes.'
        });
      } else {
        await interaction.reply({
          content: '❌ System error occurred. Please try again in a few minutes.'
        });
      }
    } catch (emergencyError) {
      console.error('❌ Emergency response failed:', emergencyError);
    }
  }
}

// Auto-register Discord user function
async function autoRegisterDiscordUser(interaction: any) {
  try {
    const discordId = interaction.user.id;
    const username = interaction.user.username;
    const discriminator = interaction.user.discriminator;
    const avatarURL = interaction.user.displayAvatarURL();
    
    // Check if user already exists
    let user = await storage.getUserByDiscordId(discordId);
    
    if (!user) {
      // Create new user
      user = await storage.createUser({
        username: username,
        discordId: discordId,
        email: `${username}@discord.temp`,
        points: 0,
        title: '',
        roles: [],
        avatar: avatarURL,
        joinDate: new Date(),
        lastActivity: new Date(),
        messageCount: 0,
        voiceMinutes: 0,
        isActive: true
      });
      
      console.log(`✅ Auto-registered new user: ${username} (ID: ${user.id})`);
    }
    
    return user;
  } catch (error) {
    console.error('Failed to auto-register user:', error);
    throw new Error('Auto-registration failed');
  }
}

// Discord bot connection function
async function connectDiscordBot(token: string) {
  try {
    // Disconnect existing client if any
    if (discordClient) {
      discordClient.destroy();
      discordClient = null;
    }

    // Create new Discord client
    discordClient = new Client({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.GuildMessages
        // Note: GuildPresences removed as it may cause issues
      ]
    });

    // Enhanced event handlers with reconnection logic
    discordClient.once(Events.ClientReady, async (client) => {
      console.log(`Discord bot ready as ${client.user.tag}`);
      
      // Update bot status with real data
      const guilds = client.guilds.cache;
      const totalMembers = guilds.reduce((acc, guild) => acc + guild.memberCount, 0);
      
      botConfig.status = {
        connected: true,
        ready: true,
        latency: client.ws.ping,
        uptime: Date.now(),
        guilds: guilds.size,
        users: totalMembers,
        error: null
      };

      // Broadcast bot status update to all WebSocket clients
      const statusMessage = JSON.stringify({
        type: 'botStatus',
        data: botConfig.status
      });

      wsClients.forEach(ws => {
        if (ws.readyState === WebSocket.OPEN) {
          ws.send(statusMessage);
        }
      });

      console.log('📡 Broadcasted bot status update to WebSocket clients');

      // Register slash commands from database with enhanced error handling
      try {
        await registerDatabaseSlashCommands(client);
        console.log('✅ Slash commands registration completed successfully');
      } catch (commandError) {
        console.error('❌ Failed to register slash commands:', commandError);
        // Continue bot operation even if command registration fails
      }
      
      // Fetch real server data from first guild
      const firstGuild = guilds.first();
      if (firstGuild) {
        try {
          // Fetch all members
          await firstGuild.members.fetch();
          
          // Get real channel data
          const channels = firstGuild.channels.cache
            .filter(channel => channel.type === 0) // Text channels
            .map(channel => ({
              id: channel.id,
              name: channel.name,
              type: channel.type,
              category: channel.parent?.name || '未分类'
            }));

          // Get real role data
          const roles = firstGuild.roles.cache
            .filter(role => !role.managed && role.name !== '@everyone')
            .map(role => ({
              id: role.id,
              name: role.name,
              color: role.hexColor,
              memberCount: role.members.size
            }));

          realServerData = {
            memberCount: firstGuild.memberCount,
            onlineCount: firstGuild.members.cache.filter(member => member.presence?.status === 'online').size,
            activeChannels: channels.length,
            channels: channels.slice(0, 10), // Limit to 10 channels
            roles: roles.slice(0, 10) // Limit to 10 roles
          };

          console.log(`Fetched real data: ${realServerData.memberCount} members, ${realServerData.activeChannels} channels, ${realServerData.roles.length} roles`);
        } catch (error) {
          console.error('Error fetching guild data:', error);
        }
      }
      
      botConfig.isConnected = true;
    });

    // Handle slash command interactions with enhanced stability and debugging
    discordClient.on(Events.InteractionCreate, async (interaction) => {
      console.log(`🎯 [${new Date().toISOString()}] Interaction received: type=${interaction.type}, isCommand=${interaction.isChatInputCommand()}`);
      
      if (!interaction.isChatInputCommand()) {
        console.log('❌ Not a chat input command, ignoring...');
        return;
      }

      const { commandName } = interaction;
      console.log(`🎯 [${new Date().toISOString()}] Received Discord command: ${commandName} from user: ${interaction.user.username} (${interaction.user.id})`);
      console.log(`🔧 Command details: guild=${interaction.guild?.name}, channel=${interaction.channel?.name} (${interaction.channel?.id})`);

      try {
        switch (commandName) {
          case 'checkin':
            console.log(`🔄 [${new Date().toISOString()}] Executing checkin command...`);
            await handleCheckinCommand(interaction);
            console.log(`✅ [${new Date().toISOString()}] Checkin command completed successfully`);
            break;
          case 'points':
            console.log(`🔄 [${new Date().toISOString()}] Executing points command...`);
            await handlePointsCommand(interaction);
            console.log(`✅ [${new Date().toISOString()}] Points command completed successfully`);
            break;
          case 'leaderboard':
            console.log(`🔄 [${new Date().toISOString()}] Executing leaderboard command...`);
            await handleLeaderboardCommand(interaction);
            console.log(`✅ [${new Date().toISOString()}] Leaderboard command completed successfully`);
            break;
          case 'code':
            console.log(`🔄 [${new Date().toISOString()}] Executing code command...`);
            await handleCodeCommand(interaction, globalBroadcast);
            console.log(`✅ [${new Date().toISOString()}] Code command completed successfully`);
            break;
          default:
            console.log(`❓ Unknown command: ${commandName}`);
            console.log(`🔧 Available commands should be: checkin, points, leaderboard, code`);
            if (!interaction.replied && !interaction.deferred) {
              await interaction.reply({
                content: `❌ Unknown command: ${commandName}. Available commands: checkin, points, leaderboard, code`,
                ephemeral: true
              });
            }
        }
      } catch (error) {
        console.error(`❌ [${new Date().toISOString()}] CRITICAL ERROR handling command ${commandName}:`, error);
        console.error(`🔧 Error message:`, error.message);
        console.error(`🔧 Error stack:`, error.stack);
        console.error(`🔧 Error details:`, JSON.stringify(error, null, 2));
        
        try {
          const errorMessage = `❌ Command failed: ${error.message || 'Unknown error'}. Please try again in a few minutes.`;
          if (interaction.replied || interaction.deferred) {
            await interaction.editReply({
              content: errorMessage
            });
          } else {
            await interaction.reply({
              content: errorMessage,
              ephemeral: true
            });
          }
        } catch (replyError) {
          console.error(`❌ [${new Date().toISOString()}] Failed to send error reply:`, replyError);
        }
      }
    });

    // Enhanced Discord connection event handlers for stability
    discordClient.on('error', (error) => {
      console.error('Discord client error:', error);
      botConfig.status.error = error.message;
      botConfig.status.connected = false;
      
      // Attempt reconnection after 30 seconds
      setTimeout(() => {
        console.log('🔄 Attempting Discord reconnection...');
        connectDiscordBot(token);
      }, 30000);
    });

    discordClient.on('disconnect', () => {
      console.log('⚠️ Discord client disconnected');
      botConfig.status.connected = false;
    });

    discordClient.on('resume', () => {
      console.log('✅ Discord client resumed');
      botConfig.status.connected = true;
      botConfig.status.error = null;
    });

    discordClient.on('reconnecting', () => {
      console.log('🔄 Discord client reconnecting...');
      botConfig.status.connected = false;
    });

    discordClient.on('shardError', (error) => {
      console.error('Discord shard error:', error);
      botConfig.status.error = error.message;
    });

    discordClient.on('shardReady', () => {
      console.log('✅ Discord shard ready');
      botConfig.status.connected = true;
      botConfig.status.error = null;
      botConfig.status.ready = false;
    });

    discordClient.on('disconnect', () => {
      console.log('Discord bot disconnected');
      botConfig.status.connected = false;
      botConfig.status.ready = false;
      botConfig.isConnected = false;
    });

    // Login to Discord
    await discordClient.login(token);
    
  } catch (error) {
    console.error('Failed to connect Discord bot:', error);
    botConfig.status.error = (error as Error).message;
    botConfig.status.connected = false;
    botConfig.status.ready = false;
    throw error;
  }
}

// Test Discord token function
async function testDiscordToken(token: string) {
  try {
    const testClient = new Client({
      intents: [GatewayIntentBits.Guilds]
    });

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        testClient.destroy();
        resolve({
          success: false,
          message: '连接超时'
        });
      }, 10000);

      testClient.once(Events.ClientReady, (client) => {
        clearTimeout(timeout);
        const guilds = client.guilds.cache;
        const totalMembers = guilds.reduce((acc, guild) => acc + guild.memberCount, 0);
        
        testClient.destroy();
        resolve({
          success: true,
          message: 'Token验证成功',
          data: {
            bot_name: client.user.tag,
            guilds: guilds.size,
            users: totalMembers
          }
        });
      });

      testClient.on('error', (error) => {
        clearTimeout(timeout);
        testClient.destroy();
        resolve({
          success: false,
          message: '连接失败: ' + error.message
        });
      });

      testClient.login(token).catch((error) => {
        clearTimeout(timeout);
        resolve({
          success: false,
          message: '连接失败: ' + error.message
        });
      });
    });
  } catch (error) {
    return {
      success: false,
      message: '连接失败: ' + (error as Error).message
    };
  }
}

// Register slash commands from database
async function registerDatabaseSlashCommands(client: any) {
  try {
    console.log('📝 Registering slash commands...');

    // Skip clearing commands to avoid conflicts - just register directly

    
    // Get all enabled commands from database with fallback
    let dbCommands = [];
    try {
      dbCommands = await storage.getAllSlashCommands();
    } catch (error) {
      console.error('❌ Failed to fetch slash commands from database:', error);
      console.log('🔄 Using hardcoded fallback commands...');
      
      // Hardcoded fallback commands
      dbCommands = [
        {
          name: 'checkin',
          description: 'Daily check-in to earn points',
          isEnabled: true,
          category: 'system'
        },
        {
          name: 'points',
          description: 'Check your current points and ranking',
          isEnabled: true,
          category: 'system'
        },
        {
          name: 'leaderboard',
          description: 'View the top users leaderboard',
          isEnabled: true,
          category: 'system'
        },
        {
          name: 'code',
          description: 'Redeem a special code for rewards',
          isEnabled: true,
          category: 'system'
        }
      ];
    }
    
    const enabledCommands = dbCommands.filter(cmd => cmd.isEnabled);
    
    if (enabledCommands.length === 0) {
      console.log('⚠️ No enabled slash commands found, using fallback commands');
      enabledCommands.push(...[
        { name: 'checkin', description: 'Daily check-in to earn points', isEnabled: true },
        { name: 'points', description: 'Check your current points and ranking', isEnabled: true },
        { name: 'leaderboard', description: 'View the top users leaderboard', isEnabled: true },
        { name: 'code', description: 'Redeem a special code for rewards', isEnabled: true }
      ]);
    }
    
    // Convert database commands to Discord command format
    const discordCommands = enabledCommands.map(cmd => ({
      name: cmd.name,
      description: cmd.description || 'Bot command',
      options: cmd.name === 'code' ? [
        {
          name: 'code',
          description: 'The redemption code to use',
          type: 3, // STRING type
          required: true
        }
      ] : []
    }));

    console.log(`🔧 Attempting to register ${discordCommands.length} commands:`, discordCommands);

    // Register commands to each guild (server) for immediate effect
    const allGuilds = client.guilds.cache;
    let totalRegistered = 0;

    for (const guild of allGuilds.values()) {
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {
          await guild.commands.set(discordCommands);
          console.log(`✅ Successfully registered ${discordCommands.length} slash commands to guild: ${guild.name} (attempt ${attempt})`);
          totalRegistered++;
          break;
        } catch (regError) {
          console.error(`❌ Command registration failed for guild ${guild.name} (attempt ${attempt}):`, regError);
          if (attempt === 3) {
            console.error(`❌ Failed to register commands to guild ${guild.name} after 3 attempts`);
          } else {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        }
      }
    }

    // Also register globally as backup (takes longer but ensures coverage)
    try {
      await client.application.commands.set(discordCommands);
      console.log(`✅ Successfully registered ${discordCommands.length} slash commands globally as backup`);
    } catch (globalError) {
      console.error('❌ Global command registration failed:', globalError);
    }
    
    // Log the registered command names
    console.log(`📋 Registered commands: ${discordCommands.map(cmd => cmd.name).join(', ')}`);
    
    // Verify commands were registered
    setTimeout(async () => {
      try {
        const registeredCommands = await client.application.commands.fetch();
        console.log(`🔍 Verification: Found ${registeredCommands.size} commands in Discord:`, 
                   registeredCommands.map(cmd => cmd.name).join(', '));
      } catch (verifyError) {
        console.error('❌ Failed to verify command registration:', verifyError);
      }
    }, 3000);
    
  } catch (error) {
    console.error('❌ Failed to register database slash commands:', error);
    console.error('🔧 Stack trace:', error.stack);
  }
}

// Variable replacement function for Discord commands
async function replaceVariables(content: string, user: any): Promise<string> {
  let result = content;
  
  try {
    // Basic user variables
    result = result.replace(/{user_username}/g, user.username || 'Unknown');
    result = result.replace(/{user_points}/g, (user.points || 0).toString());
    result = result.replace(/{user_title}/g, user.title || (user.roles && user.roles.length > 0 ? user.roles[0] : 'No Title'));
    
    // Calculate user rank
    const allUsers = await storage.getAllUsers();
    const sortedUsers = allUsers.sort((a, b) => (b.points || 0) - (a.points || 0));
    const userRank = sortedUsers.findIndex(u => u.id === user.id) + 1;
    result = result.replace(/{user_rank}/g, userRank.toString());
    
    // Get point records
    const pointRecords = await storage.getUserPointRecords(user.id);
    result = result.replace(/{point_records}/g, pointRecords.length.toString());
    
    // Recent point records
    const recentRecords = pointRecords
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);
    
    const recentRecordsText = recentRecords.length > 0 
      ? recentRecords.map(record => {
          const date = new Date(record.createdAt).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
          });
          const sign = record.type === 'earned' ? '+' : '-';
          return `• ${date}: ${sign}${record.amount} points - ${record.reason}`;
        }).join('\n')
      : 'No recent records available';
    
    result = result.replace(/{recent_point_records}/g, recentRecordsText);
    
    // Add timestamp
    result = result.replace(/{timestamp}/g, new Date().toLocaleString('en-US', {
      timeZone: 'UTC',
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    }));
    
  } catch (error) {
    console.error('Error in variable replacement:', error);
    // Return original content if replacement fails
    return content;
  }
  
  return result;
}

// Handle points command
async function handlePointsCommand(interaction: any) {
  try {
    // Get command from database first
    let pointsCommand;
    try {
      const commands = await storage.getAllSlashCommands();
      pointsCommand = commands.find(cmd => cmd.name === 'points');
    } catch (dbError) {
      console.error('Database error getting points command:', dbError);
      pointsCommand = null;
    }

    // Check interaction state before responding
    if (interaction.replied || interaction.deferred) {
      await interaction.editReply({ content: '⏳ Checking your points...' });
    } else {
      await interaction.reply({ content: '⏳ Checking your points...', ephemeral: false });
    }
    
    const user = await autoRegisterDiscordUser(interaction);
    if (!user) {
      await interaction.editReply({ content: '❌ Failed to find user data.' });
      return;
    }

    // If we have a custom response from database, use it with variable replacement
    if (pointsCommand && pointsCommand.response) {
      const processedResponse = await replaceVariables(pointsCommand.response, user);
      
      // Check if it's an embed format or plain text
      if (processedResponse.includes('**') || processedResponse.includes('*')) {
        // It's formatted text, create embed
        const embed = {
          color: 0xF39C12,
          description: processedResponse,
          timestamp: new Date().toISOString()
        };
        await interaction.editReply({ embeds: [embed] });
      } else {
        // Plain text response
        await interaction.editReply({ content: processedResponse });
      }
      return;
    }

    // Fallback to original logic if no custom response
    // Get user's point records
    const pointRecords = await storage.getUserPointRecords(user.id);
    const totalRecords = pointRecords.length;
    
    // Calculate user rank
    const allUsers = await storage.getAllUsers();
    const sortedUsers = allUsers.sort((a, b) => (b.points || 0) - (a.points || 0));
    const userRank = sortedUsers.findIndex(u => u.id === user.id) + 1;
    
    // Get recent records (last 5)
    const recentRecords = pointRecords
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      .slice(0, 5);
    
    // Format recent records text
    const recentRecordsText = recentRecords.length > 0 
      ? recentRecords.map(record => {
          const date = new Date(record.createdAt).toLocaleDateString('en-US', {
            month: 'short',
            day: 'numeric',
            year: 'numeric'
          });
          const sign = record.type === 'earned' ? '+' : '-';
          return `• ${date}: ${sign}${record.amount} points - ${record.reason}`;
        }).join('\n')
      : 'No recent records';

    // Get user's current title/role
    const currentTitle = user.title || (user.roles && user.roles.length > 0 ? user.roles[0] : 'No Title');

    const embed = {
      color: 0xF39C12,
      author: {
        name: 'Points Information',
        icon_url: user.avatar || 'https://cdn.discordapp.com/embed/avatars/0.png'
      },
      description: `@${user.username}`,
      fields: [
        {
          name: 'Current Points',
          value: `${user.points || 0}`,
          inline: true
        },
        {
          name: 'Rank',
          value: `#${userRank}`,
          inline: true
        },
        {
          name: 'Total Records',
          value: `${totalRecords}`,
          inline: true
        },
        {
          name: 'Current Title',
          value: currentTitle,
          inline: false
        },
        {
          name: 'Recent Records',
          value: recentRecordsText,
          inline: false
        }
      ],
      footer: {
        text: `Total: ${user.points || 0} points`
      },
      timestamp: new Date().toISOString()
    };

    await interaction.editReply({ embeds: [embed] });
  } catch (error) {
    console.error('❌ Points command failed:', error);
    if (interaction.replied || interaction.deferred) {
      await interaction.editReply({ content: '❌ Failed to get points data.' });
    }
  }
}

// Handle leaderboard command
async function handleLeaderboardCommand(interaction: any) {
  try {
    // Check interaction state before responding
    if (interaction.replied || interaction.deferred) {
      await interaction.editReply({ content: '⏳ Loading leaderboard...' });
    } else {
      await interaction.reply({ content: '⏳ Loading leaderboard...', ephemeral: false });
    }
    
    const allUsers = await storage.getAllUsers();
    const topUsers = allUsers
      .sort((a, b) => (b.points || 0) - (a.points || 0))
      .slice(0, 10);

    if (topUsers.length === 0) {
      await interaction.editReply({ content: '📊 No leaderboard data available.' });
      return;
    }

    // Format leaderboard with top 3 special symbols
    const leaderboardText = topUsers.map((user, index) => {
      const rank = index + 1;
      let symbol = '';
      
      if (rank === 1) symbol = '🥇';
      else if (rank === 2) symbol = '🥈';
      else if (rank === 3) symbol = '🥉';
      else symbol = `${rank}.`;
      
      // Get user's title or role
      const userTitle = user.titles && user.titles.length > 0 ? user.titles[0] : 'Member';
      
      return `${symbol} **${user.username}** - ${user.points || 0} points`;
    }).join('\n');

    const embed = {
      color: 0xF39C12,
      title: '🏆 Points Leaderboard',
      description: `🌟 **TOP 10 RANKINGS**\n\n${leaderboardText}`,
      footer: {
        text: `⏰ Last Updated: ${new Date().toLocaleDateString('en-US', { 
          month: 'short', 
          day: 'numeric', 
          year: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}`
      },
      timestamp: new Date().toISOString()
    };

    await interaction.editReply({ embeds: [embed] });
  } catch (error) {
    console.error('❌ Leaderboard command failed:', error);
    if (interaction.replied || interaction.deferred) {
      await interaction.editReply({ content: '❌ Failed to load leaderboard.' });
    }
  }
}

// Handle code command
async function handleCodeCommand(interaction: any, broadcastFn?: (data: any) => void) {
  try {
    // Check interaction state before responding
    if (interaction.replied || interaction.deferred) {
      await interaction.editReply({ content: '⏳ Processing code...' });
    } else {
      await interaction.reply({ content: '⏳ Processing code...', ephemeral: true });
    }
    
    const code = interaction.options?.getString('code');
    if (!code) {
      await interaction.editReply({ content: '❌ Please provide a code to redeem.' });
      return;
    }

    const user = await autoRegisterDiscordUser(interaction);
    if (!user) {
      await interaction.editReply({ content: '❌ Failed to find user data.' });
      return;
    }

    // Check if code is valid in database
    const validCodes = await db.select().from(redemptionCodes).where(eq(redemptionCodes.code, code));
    if (validCodes.length === 0) {
      await interaction.editReply({ content: '❌ Invalid code.' });
      return;
    }

    const redemptionCode = validCodes[0];
    
    // Check if code is active and not expired
    if (!redemptionCode.is_active) {
      await interaction.editReply({ content: '❌ This code is no longer active.' });
      return;
    }
    
    if (redemptionCode.expires_at && new Date() > redemptionCode.expires_at) {
      await interaction.editReply({ content: '❌ This code has expired.' });
      return;
    }

    // Check if user already redeemed
    const hasRedeemed = await storage.hasRedeemedCode(user.id, code);
    if (hasRedeemed) {
      await interaction.editReply({ content: '❌ You have already redeemed this code.' });
      return;
    }

    // Check usage limits
    if (redemptionCode.max_uses && redemptionCode.current_uses >= redemptionCode.max_uses) {
      await interaction.editReply({ content: '❌ This code has reached its usage limit.' });
      return;
    }

    // Redeem code
    await storage.createPointRecord({
      userId: user.id,
      amount: redemptionCode.points_reward,
      reason: `Code redemption: ${code}`,
      type: 'earned'
    });

    await storage.recordCodeRedemption(user.id, code);

    // Update code usage count
    await db.update(redemptionCodes)
      .set({ current_uses: (redemptionCode.current_uses || 0) + 1 })
      .where(eq(redemptionCodes.id, redemptionCode.id));

    await interaction.editReply({ content: `✅ Code redeemed successfully! You earned ${redemptionCode.points_reward} points.` });
    
    // Broadcast update to WebSocket clients if broadcast function exists
    if (broadcastFn && typeof broadcastFn === 'function') {
      broadcastFn({
        type: 'codeRedemption',
        data: {
          userId: user.id,
          username: user.username,
          code: code,
          points: redemptionCode.points_reward
        }
      });
    }
  } catch (error) {
    console.error('❌ Code command failed:', error);
    if (interaction.replied || interaction.deferred) {
      await interaction.editReply({ content: '❌ Failed to process code.' });
    }
  }
}

// Force clean all Discord commands function
async function forceCleanAllDiscordCommands(client: any) {
  try {
    console.log('🧹 FORCE CLEARING ALL Discord commands (global and guild-specific)...');
    
    // Clear guild commands
    const guilds = client.guilds.cache;
    for (const [guildId, guild] of guilds) {
      try {
        await guild.commands.set([]);
        console.log(`✅ Cleared commands for guild: ${guild.name}`);
      } catch (error) {
        console.error(`❌ Failed to clear commands for guild ${guild.name}:`, error);
      }
    }
    
    // Clear global commands
    try {
      await client.application?.commands.set([]);
      console.log('✅ Cleared all global commands');
    } catch (error) {
      console.error('❌ Failed to clear global commands:', error);
    }
    
    console.log('✅ Cleared all existing Discord commands');
  } catch (error) {
    console.error('❌ Error during force command cleanup:', error);
  }
}

export default registerRoutes;